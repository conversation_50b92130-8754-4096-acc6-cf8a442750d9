# 环境搭建指南

## 🚀 开发环境搭建

本指南将帮助你搭建DJOS项目的完整开发环境，包括Java开发环境、数据库、中间件等。

## 📋 环境要求

### 硬件要求
- **内存**: 8GB以上（推荐16GB）
- **硬盘**: 50GB以上可用空间
- **操作系统**: macOS 10.14+ 或 Windows 10+ 或 Ubuntu 18.04+

### 软件要求
- **JDK**: 8+ (推荐OpenJDK 8)
- **Maven**: 3.6+
- **Docker**: 20.10+
- **Git**: 2.20+
- **IDE**: Cursor (推荐) 或 IntelliJ IDEA

## 🛠️ 步骤一：基础软件安装

### macOS安装

#### 1. 安装Homebrew
```bash
# 安装Homebrew
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# 验证安装
brew --version
```

#### 2. 安装JDK 8
```bash
# 安装OpenJDK 8
brew install openjdk@8

# 设置环境变量
echo 'export JAVA_HOME=/usr/local/opt/openjdk@8' >> ~/.zshrc
echo 'export PATH="$JAVA_HOME/bin:$PATH"' >> ~/.zshrc

# 使环境变量生效
source ~/.zshrc

# 验证安装
java -version
```

#### 3. 安装Maven
```bash
# 安装Maven
brew install maven

# 验证安装
mvn -version
```

#### 4. 安装Git
```bash
# 安装Git
brew install git

# 配置Git
git config --global user.name "你的名字"
git config --global user.email "你的邮箱"

# 验证安装
git --version
```

#### 5. 安装Docker Desktop
```bash
# 下载Docker Desktop
# 访问 https://www.docker.com/products/docker-desktop 下载并安装

# 验证安装
docker --version
docker-compose --version
```

### Windows安装

#### 1. 安装JDK 8
```powershell
# 下载OpenJDK 8
# 访问 https://adoptopenjdk.net/ 下载Windows版本的OpenJDK 8

# 设置环境变量
# 1. 右键"此电脑" -> "属性" -> "高级系统设置" -> "环境变量"
# 2. 新建系统变量 JAVA_HOME，值为JDK安装路径
# 3. 在Path变量中添加 %JAVA_HOME%\bin

# 验证安装
java -version
```

#### 2. 安装Maven
```powershell
# 下载Maven
# 访问 https://maven.apache.org/download.cgi 下载Maven

# 设置环境变量
# 1. 新建系统变量 MAVEN_HOME，值为Maven安装路径
# 2. 在Path变量中添加 %MAVEN_HOME%\bin

# 验证安装
mvn -version
```

#### 3. 安装Git
```powershell
# 下载Git
# 访问 https://git-scm.com/download/win 下载并安装

# 验证安装
git --version
```

#### 4. 安装Docker Desktop
```powershell
# 下载Docker Desktop for Windows
# 访问 https://www.docker.com/products/docker-desktop 下载并安装

# 验证安装
docker --version
docker-compose --version
```

## 🎨 步骤二：开发工具配置

### Cursor编辑器配置

#### 1. 安装Cursor
```bash
# 下载Cursor
# 访问 https://cursor.sh 下载并安装
```

#### 2. 安装推荐插件
在Cursor中安装以下插件：
- **Java Extension Pack**: Java开发必备
- **Spring Boot Extension Pack**: Spring Boot开发
- **Maven for Java**: Maven项目管理
- **Docker**: Docker容器管理
- **GitLens**: Git增强工具
- **XML**: XML文件支持

#### 3. 配置Maven镜像
创建或编辑 `~/.m2/settings.xml` 文件：
```xml
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
                              http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <mirrors>
        <mirror>
            <id>aliyun</id>
            <mirrorOf>central</mirrorOf>
            <name>Aliyun Maven Central</name>
            <url>https://maven.aliyun.com/repository/central</url>
        </mirror>
        <mirror>
            <id>aliyun-spring</id>
            <mirrorOf>spring</mirrorOf>
            <name>Aliyun Spring</name>
            <url>https://maven.aliyun.com/repository/spring</url>
        </mirror>
    </mirrors>
    
    <profiles>
        <profile>
            <id>jdk-1.8</id>
            <activation>
                <activeByDefault>true</activeByDefault>
                <jdk>1.8</jdk>
            </activation>
            <properties>
                <maven.compiler.source>1.8</maven.compiler.source>
                <maven.compiler.target>1.8</maven.compiler.target>
                <maven.compiler.compilerVersion>1.8</maven.compiler.compilerVersion>
            </properties>
        </profile>
    </profiles>
</settings>
```

## 🐳 步骤三：Docker环境搭建

### 启动基础服务

#### 1. 创建docker-compose.yml文件
在项目根目录创建 `docker-compose.yml` 文件：
```yaml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: djos
      MYSQL_USER: djos
      MYSQL_PASSWORD: djos123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - djos-network
    command: --default-authentication-plugin=mysql_native_password

  redis:
    image: redis:6.2
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - djos-network

  nacos:
    image: nacos/nacos-server:2.0.4
    container_name: nacos
    ports:
      - "8848:8848"
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: password
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    volumes:
      - nacos_logs:/home/<USER>/logs
    networks:
      - djos-network
    depends_on:
      - mysql

  rocketmq-nameserver:
    image: foxiswho/rocketmq:server-4.9.4
    container_name: rocketmq-nameserver
    ports:
      - "9876:9876"
    volumes:
      - rocketmq_nameserver_logs:/home/<USER>/logs
    networks:
      - djos-network

  rocketmq-broker:
    image: foxiswho/rocketmq:broker-4.9.4
    container_name: rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    environment:
      NAMESRV_ADDR: rocketmq-nameserver:9876
    volumes:
      - rocketmq_broker_logs:/home/<USER>/logs
      - rocketmq_broker_store:/home/<USER>/store
    networks:
      - djos-network
    depends_on:
      - rocketmq-nameserver

  rocketmq-console:
    image: styletang/rocketmq-console-ng
    container_name: rocketmq-console
    ports:
      - "8081:8080"
    environment:
      JAVA_OPTS: -Drocketmq.namesrv.addr=rocketmq-nameserver:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    networks:
      - djos-network
    depends_on:
      - rocketmq-nameserver

networks:
  djos-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  nacos_logs:
    driver: local
  rocketmq_nameserver_logs:
    driver: local
  rocketmq_broker_logs:
    driver: local
  rocketmq_broker_store:
    driver: local
```

#### 2. 创建数据库初始化脚本
在项目根目录创建 `init.sql` 文件：
```sql
-- 创建数据库
CREATE DATABASE IF NOT EXISTS djos DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建nacos数据库
CREATE DATABASE IF NOT EXISTS nacos DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 使用djos数据库
USE djos;

-- 创建测试表
CREATE TABLE IF NOT EXISTS `user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `email` varchar(100) NOT NULL COMMENT '邮箱',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 插入测试数据
INSERT INTO `user` (`username`, `email`, `password`, `status`) VALUES
('admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVYITi', 1),
('test', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVYITi', 1);
```

#### 3. 启动基础服务
```bash
# 在项目根目录执行
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看服务日志
docker-compose logs -f mysql
docker-compose logs -f redis
docker-compose logs -f nacos
```

#### 4. 验证服务
```bash
# 测试MySQL连接
docker exec -it mysql mysql -uroot -ppassword -e "SHOW DATABASES;"

# 测试Redis连接
docker exec -it redis redis-cli ping

# 测试Nacos访问
# 打开浏览器访问 http://localhost:8848/nacos
# 用户名：nacos，密码：nacos

# 测试RocketMQ控制台
# 打开浏览器访问 http://localhost:8081
```

## 📦 步骤四：项目构建

### 1. 克隆项目
```bash
# 克隆项目
git clone <your-repo-url>
cd DJOS

# 查看项目结构
ls -la
```

### 2. 构建项目
```bash
# 清理并编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package -DskipTests

# 或者构建整个项目
mvn clean install -DskipTests
```

### 3. 运行单个服务
```bash
# 进入服务目录
cd hobbits-ms-harbor

# 构建服务
mvn clean package -DskipTests

# 运行服务
java -jar target/hobbits-ms-harbor-1.0.0.jar

# 或者使用Maven运行
mvn spring-boot:run
```

## 🔧 步骤五：IDE配置

### Cursor项目配置

#### 1. 导入项目
```bash
# 在Cursor中打开项目
# File -> Open Folder -> 选择项目根目录
```

#### 2. 配置项目SDK
- 在Cursor中打开项目
- 按 `Cmd+,` 打开设置
- 搜索 "Java"
- 选择已安装的JDK 8

#### 3. 配置Maven
- 在Cursor中打开项目
- 按 `Cmd+,` 打开设置
- 搜索 "Maven"
- 设置Maven home路径
- 设置settings.xml文件路径

#### 4. 生成项目结构
- 在Cursor中右键项目根目录
- 选择 "Maven" -> "Update Project"
- 等待依赖下载完成

## 🧪 步骤六：环境验证

### 1. 验证Java环境
```bash
# 验证Java版本
java -version

# 验证Maven版本
mvn -version

# 验证Git版本
git --version

# 验证Docker版本
docker --version
docker-compose --version
```

### 2. 验证数据库连接
```bash
# 测试MySQL连接
docker exec -it mysql mysql -uroot -ppassword -e "SELECT 1;"

# 测试Redis连接
docker exec -it redis redis-cli ping

# 测试Nacos连接
curl -X GET 'http://localhost:8848/nacos/v1/ns/instance/list?serviceName=nacos'
```

### 3. 验证项目运行
```bash
# 进入hobbits-ms-harbor目录
cd hobbits-ms-harbor

# 启动服务
mvn spring-boot:run

# 等待服务启动完成
# 测试接口
curl http://localhost:8080/actuator/health
```

## 🐛 常见问题解决

### 1. Maven依赖下载失败
```bash
# 清理Maven缓存
mvn clean install -U

# 或者手动删除缓存
rm -rf ~/.m2/repository
mvn clean install
```

### 2. Docker服务启动失败
```bash
# 检查Docker状态
docker info

# 重启Docker服务
# macOS: 通过Docker Desktop重启
# Linux: sudo systemctl restart docker

# 查看容器日志
docker-compose logs mysql
docker-compose logs redis
```

### 3. 端口冲突
```bash
# 检查端口占用
lsof -i :3306
lsof -i :6379
lsof -i :8848

# 修改docker-compose.yml中的端口映射
ports:
  - "3307:3306"  # 将MySQL端口改为3307
```

### 4. 内存不足
```bash
# 修改Docker Desktop内存设置
# 打开Docker Desktop -> Settings -> Resources -> Advanced
# 增加内存分配（建议8GB以上）

# 或者减少服务内存使用
# 在docker-compose.yml中添加环境变量
environment:
  JAVA_OPTS: "-Xmx512m -Xms512m"
```

### 5. Nacos启动失败
```bash
# 检查MySQL连接
docker exec -it mysql mysql -uroot -ppassword -e "SHOW DATABASES;"

# 检查Nacos日志
docker-compose logs nacos

# 重新初始化Nacos数据库
docker exec -it mysql mysql -uroot -ppassword -e "DROP DATABASE IF EXISTS nacos; CREATE DATABASE nacos;"
```

## 📚 开发工具快捷键

### Cursor常用快捷键

#### 通用快捷键
- `Cmd+,`: 打开设置
- `Cmd+Shift+P`: 打开命令面板
- `Cmd+P`: 快速打开文件
- `Cmd+Shift+F`: 全局搜索
- `Cmd+Shift+G`: 全局替换

#### 编辑快捷键
- `Cmd+D`: 选择相同内容
- `Cmd+Shift+L`: 选择所有相同内容
- `Cmd+/: 注释/取消注释
- `Cmd+Shift+↑/↓`: 移动行
- `Cmd+Shift+K`: 删除行

#### 调试快捷键
- `F5`: 开始调试
- `Shift+F5`: 停止调试
- `F10`: 单步跳过
- `F11`: 单步进入
- `Shift+F11`: 单步退出

#### Git快捷键
- `Cmd+Shift+G`: 打开Git面板
- `Cmd+Enter`: 提交更改
- `Cmd+Shift+Enter`: 推送更改

## 🎯 环境搭建检查清单

### 基础软件安装
- [ ] JDK 8安装完成
- [ ] Maven安装完成
- [ ] Git安装完成
- [ ] Docker Desktop安装完成

### 开发工具配置
- [ ] Cursor编辑器安装完成
- [ ] 必要插件安装完成
- [ ] Maven镜像配置完成
- [ ] 项目导入成功

### Docker环境搭建
- [ ] docker-compose.yml文件创建
- [ ] 数据库初始化脚本创建
- [ ] 基础服务启动成功
- [ ] 服务连接验证通过

### 项目构建验证
- [ ] 项目克隆完成
- [ ] Maven依赖下载完成
- [ ] 项目构建成功
- [ ] 服务启动验证通过

### 环境验证
- [ ] 所有服务正常运行
- [ ] 数据库连接正常
- [ ] API接口可访问
- [ ] 开发工具配置正确

## 🚀 下一步

恭喜你！环境搭建已经完成。现在你可以：

1. **开始学习**：参考 [PHP转Java学习计划](./php-to-java-learning-plan.md)
2. **运行项目**：尝试运行不同的微服务模块
3. **开发功能**：从简单的CRUD功能开始开发
4. **部署服务**：学习如何部署到Docker容器

如果你在环境搭建过程中遇到任何问题，请参考上面的常见问题解决部分，或者在团队中寻求帮助。祝你开发顺利！