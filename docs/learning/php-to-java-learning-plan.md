# PHP转Java学习计划

## 🎯 概述

本文档为PHP开发工程师提供的DJOS项目Java转学习指南，包含完整的学习路径、环境搭建、实践任务和AI辅助开发技巧。

## 📋 学习计划总览

### 学习阶段划分
- **第一阶段**：Java基础与环境搭建（2-3周）
- **第二阶段**：DJOS项目架构理解（1-2周）
- **第三阶段**：实践开发（2-3周）
- **第四阶段**：部署与运维（1周）
- **第五阶段**：AI辅助开发技巧（持续学习）

### 预期学习成果
- 掌握Java基础语法和Spring Boot框架
- 理解微服务架构和DJOS项目结构
- 能够独立开发和部署微服务
- 熟练使用AI工具辅助开发

## 🚀 第一阶段：Java基础与环境搭建（2-3周）

### 1. Java基础语法快速掌握（1周）

#### 重点对比PHP与Java差异

**类型系统**
- PHP：弱类型，动态类型
- Java：强类型，静态类型
```java
// Java 强类型示例
String name = "张三";
int age = 25;
List<String> hobbies = Arrays.asList("编程", "阅读");
```

**面向对象编程**
```java
// Java 接口定义
public interface UserService {
    User getUserById(Long id);
    void createUser(User user);
}

// Java 实现类
@Service
public class UserServiceImpl implements UserService {
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public User getUserById(Long id) {
        return userMapper.selectById(id);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createUser(User user) {
        userMapper.insert(user);
    }
}
```

**异常处理**
```java
// Java 异常处理
try {
    User user = userService.getUserById(id);
    return ResponseEntity.ok(user);
} catch (UserNotFoundException e) {
    return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
} catch (Exception e) {
    log.error("获取用户信息失败", e);
    return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
}
```

**集合框架**
```java
// Java 集合框架使用
List<User> users = new ArrayList<>();
Map<String, User> userMap = new HashMap<>();
Set<String> usernames = new HashSet<>();

// Java 8 Stream API
List<String> names = users.stream()
    .filter(user -> user.getAge() > 18)
    .map(User::getName)
    .collect(Collectors.toList());
```

**Lambda表达式**
```java
// Java Lambda表达式
List<User> filteredUsers = users.stream()
    .filter(user -> user.getAge() > 18)
    .collect(Collectors.toList());

// 方法引用
users.forEach(User::printInfo);
```

### 2. 开发环境搭建（2-3天）

#### 安装必要工具

**macOS环境**
```bash
# 安装JDK 8
brew install openjdk@8
echo 'export JAVA_HOME=/usr/local/opt/openjdk@8' >> ~/.zshrc

# 安装Maven
brew install maven

# 安装Docker Desktop
# 从官网下载并安装Docker Desktop

# 安装Git
brew install git

# 下载并安装Cursor编辑器
# 从官网下载Cursor
```

**配置开发环境**
```bash
# 设置Maven仓库镜像（国内加速）
mkdir -p ~/.m2
cat > ~/.m2/settings.xml << EOF
<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns="http://maven.apache.org/SETTINGS/1.0.0"
          xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.0.0 
                              http://maven.apache.org/xsd/settings-1.0.0.xsd">
    <mirrors>
        <mirror>
            <id>aliyun</id>
            <mirrorOf>central</mirrorOf>
            <name>Aliyun Maven Central</name>
            <url>https://maven.aliyun.com/repository/central</url>
        </mirror>
    </mirrors>
</settings>
EOF

# 验证安装
java -version
mvn -version
docker --version
```

**Cursor编辑器配置**
推荐安装以下插件：
- Java Extension Pack
- Spring Boot Extension Pack
- Docker
- Maven for Java
- GitLens

### 3. Spring Boot基础（3-4天）

#### 核心概念学习

**Spring Boot自动配置**
```java
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
```

**依赖注入**
```java
@Service
public class OrderService {
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private GoodsService goodsService;
    
    // 或者使用构造器注入（推荐）
    private final UserService userService;
    
    public OrderService(UserService userService) {
        this.userService = userService;
    }
}
```

**配置文件**
```yaml
# application.yml
server:
  port: 8080
  servlet:
    context-path: /api

spring:
  datasource:
    url: ********************************
    username: root
    password: password
    driver-class-name: com.mysql.cj.jdbc.Driver
  redis:
    host: localhost
    port: 6379

mybatis:
  mapper-locations: classpath:mapper/*.xml
  type-aliases-package: cn.shopex.hobbits.**.orm.model
```

**RESTful API开发**
```java
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<User>> getUser(@PathVariable Long id) {
        User user = userService.getUserById(id);
        return ResponseEntity.ok(ApiResponse.success(user));
    }
    
    @PostMapping
    public ResponseEntity<ApiResponse<User>> createUser(@Valid @RequestBody UserCreateRequest request) {
        User user = userService.createUser(request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<User>> updateUser(
            @PathVariable Long id, 
            @Valid @RequestBody UserUpdateRequest request) {
        User user = userService.updateUser(id, request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }
}
```

**数据验证**
```java
public class UserCreateRequest {
    @NotBlank(message = "用户名不能为空")
    @Size(min = 2, max = 50, message = "用户名长度必须在2-50之间")
    private String username;
    
    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "邮箱不能为空")
    private String email;
    
    @Min(value = 0, message = "年龄不能小于0")
    @Max(value = 150, message = "年龄不能大于150")
    private Integer age;
}
```

## 🏗️ 第二阶段：DJOS项目架构理解（1-2周）

### 1. 微服务架构理解（2-3天）

#### 学习服务模块

**网关层**
- **hobbits-ms-gateway**: API网关，处理外部请求
- **hobbits-bz-gateway**: 业务网关，内部服务路由

**业务微服务**
- **hobbits-ms-harbor**: 接口中心服务
- **hobbits-ms-goods**: 商品管理服务
- **hobbits-ms-order**: 订单处理服务
- **hobbits-ms-inventory**: 库存管理服务
- **hobbits-ms-member**: 会员管理服务
- **hobbits-ms-finance**: 财务服务
- **hobbits-ms-message**: 消息服务
- **hobbits-ms-label**: 标签管理服务
- **hobbits-ms-adapter**: 外部系统适配器

**基础设施**
- **hobbits-canal-client**: 数据库变更数据捕获
- **hobbits-xxl-job**: 分布式任务调度
- **manager**: 数据库管理和迁移

**核心组件**
- **Nacos**: 服务发现和配置管理
- **RocketMQ**: 消息队列
- **Redis**: 缓存
- **MySQL**: 数据库
- **Prometheus**: 监控指标

#### 微服务通信模式
```java
// 服务间调用示例（使用OpenFeign）
@FeignClient(name = "hobbits-ms-goods", path = "/api/goods")
public interface GoodsClient {
    
    @GetMapping("/{id}")
    ResponseEntity<ApiResponse<Goods>> getGoods(@PathVariable Long id);
    
    @PostMapping("/batch")
    ResponseEntity<ApiResponse<List<Goods>>> batchGetGoods(@RequestBody List<Long> ids);
}

@Service
public class OrderService {
    
    @Autowired
    private GoodsClient goodsClient;
    
    public void createOrder(OrderCreateRequest request) {
        // 调用商品服务
        ResponseEntity<ApiResponse<Goods>> response = goodsClient.getGoods(request.getGoodsId());
        Goods goods = response.getBody().getData();
        
        // 创建订单逻辑
        // ...
    }
}
```

### 2. 项目结构分析（2-3天）

#### 三层架构理解

**标准服务结构**
```
service-name/
├── orm/                   # 数据访问层
│   ├── model/            # Entity类 (@Entity)
│   └── mapper/           # MyBatis mappers (@Mapper)
├── library/              # 通用工具
│   ├── dto/              # Data Transfer Objects
│   └── enums/            # Enumerations
└── platform/             # 应用层
    ├── controller/       # REST controllers (@RestController)
    ├── biz/              # Business logic (@Service, @Component)
    └── config/           # Configuration classes
```

**代码示例**
```java
// Entity类
@Entity
@Table(name = "user")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "username")
    private String username;
    
    @Column(name = "email")
    private String email;
    
    // getters and setters
}

// Mapper接口
@Mapper
public interface UserMapper {
    @Select("SELECT * FROM user WHERE id = #{id}")
    User selectById(Long id);
    
    @Insert("INSERT INTO user (username, email) VALUES (#{username}, #{email})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(User user);
    
    @Update("UPDATE user SET username = #{username}, email = #{email} WHERE id = #{id}")
    int update(User user);
    
    @Delete("DELETE FROM user WHERE id = #{id}")
    int deleteById(Long id);
}

// Service类
@Service
public class UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Transactional(rollbackFor = Exception.class)
    public User createUser(UserCreateRequest request) {
        User user = new User();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        userMapper.insert(user);
        return user;
    }
}

// Controller类
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @PostMapping
    public ResponseEntity<ApiResponse<User>> createUser(@Valid @RequestBody UserCreateRequest request) {
        User user = userService.createUser(request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }
}
```

### 3. 数据库技术栈（2-3天）

#### MyBatis vs PHP ORM对比

**注解方式SQL**
```java
@Mapper
public interface OrderMapper {
    
    @Select("SELECT * FROM orders WHERE id = #{id}")
    Order selectById(Long id);
    
    @Select("SELECT * FROM orders WHERE user_id = #{userId} ORDER BY create_time DESC")
    List<Order> selectByUserId(Long userId);
    
    @Insert("INSERT INTO orders (user_id, total_amount, status) VALUES (#{userId}, #{totalAmount}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Order order);
    
    @Update("UPDATE orders SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") String status);
}
```

**XML映射文件**
```xml
<!-- OrderMapper.xml -->
<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" 
    "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.shopex.hobbits.order.orm.mapper.OrderMapper">
    
    <resultMap id="BaseResultMap" type="cn.shopex.hobbits.order.orm.model.Order">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="total_amount" property="totalAmount" jdbcType="DECIMAL"/>
        <result column="status" property="status" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>
    
    <select id="selectByUserIdWithItems" resultMap="OrderWithItemsResultMap">
        SELECT o.*, oi.* 
        FROM orders o
        LEFT JOIN order_items oi ON o.id = oi.order_id
        WHERE o.user_id = #{userId}
        ORDER BY o.create_time DESC
    </select>
    
</mapper>
```

**动态SQL**
```java
@Mapper
public interface GoodsMapper {
    
    @SelectProvider(type = GoodsSqlProvider.class, method = "selectByCondition")
    List<Goods> selectByCondition(GoodsQueryRequest request);
}

class GoodsSqlProvider {
    public String selectByCondition(GoodsQueryRequest request) {
        return new SQL() {{
            SELECT("*");
            FROM("goods");
            if (request.getName() != null) {
                WHERE("name LIKE CONCAT('%', #{name}, '%')");
            }
            if (request.getCategoryId() != null) {
                WHERE("category_id = #{categoryId}");
            }
            if (request.getStatus() != null) {
                WHERE("status = #{status}");
            }
            ORDER_BY("create_time DESC");
        }}.toString();
    }
}
```

**连接池配置**
```yaml
# application.yml
spring:
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ****************************************************************************************************
    username: root
    password: password
    druid:
      initial-size: 5
      min-idle: 5
      max-active: 20
      max-wait: 60000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      validation-query: SELECT 1
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      pool-prepared-statements: true
      max-pool-prepared-statement-per-connection-size: 20
```

## 🚀 第三阶段：实践开发（2-3周）

### 1. 从简单模块开始（3-4天）

#### 选择hobbits-ms-harbor接口中心服务

**步骤1：理解现有接口定义**
```bash
# 查看项目结构
cd hobbits-ms-harbor
find . -name "*.java" -type f | head -20

# 查看数据库表定义
# 在 digios-master-data/2.24.0/ 目录下查看 harbor_*.yaml 文件
```

**步骤2：添加新的简单接口**
```java
// 在 platform/controller 下添加新的控制器
@RestController
@RequestMapping("/api/harbor/demo")
public class DemoController {
    
    @Autowired
    private DemoService demoService;
    
    @GetMapping("/hello")
    public ResponseEntity<ApiResponse<String>> sayHello() {
        String message = demoService.sayHello();
        return ResponseEntity.ok(ApiResponse.success(message));
    }
    
    @PostMapping("/echo")
    public ResponseEntity<ApiResponse<String>> echo(@RequestBody EchoRequest request) {
        String message = demoService.echo(request.getMessage());
        return ResponseEntity.ok(ApiResponse.success(message));
    }
}
```

**步骤3：编写对应的Mapper和Service**
```java
// 在 orm/mapper 下添加新的Mapper
@Mapper
public interface DemoMapper {
    @Select("SELECT 'Hello, World!' as message")
    String getHelloMessage();
    
    @Select("SELECT CONCAT('Echo: ', #{message}) as message")
    String echoMessage(@Param("message") String message);
}

// 在 platform/biz 下添加新的Service
@Service
public class DemoService {
    
    @Autowired
    private DemoMapper demoMapper;
    
    public String sayHello() {
        return demoMapper.getHelloMessage();
    }
    
    public String echo(String message) {
        return demoMapper.echoMessage(message);
    }
}
```

**步骤4：编写测试用例**
```java
@SpringBootTest
public class DemoControllerTest {
    
    @Autowired
    private MockMvc mockMvc;
    
    @Test
    public void testSayHello() throws Exception {
        mockMvc.perform(get("/api/harbor/demo/hello"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("Hello, World!"));
    }
    
    @Test
    public void testEcho() throws Exception {
        String requestJson = "{\"message\":\"Hello Java\"}";
        
        mockMvc.perform(post("/api/harbor/demo/echo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("Echo: Hello Java"));
    }
}
```

### 2. 完整功能开发（4-5天）

#### 选择一个业务模块（如商品管理）

**步骤1：数据库表设计**
```sql
-- 商品表
CREATE TABLE goods (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '商品名称',
    description TEXT COMMENT '商品描述',
    price DECIMAL(10,2) NOT NULL COMMENT '商品价格',
    stock INT NOT NULL DEFAULT 0 COMMENT '库存数量',
    category_id BIGINT COMMENT '分类ID',
    status TINYINT NOT NULL DEFAULT 1 COMMENT '状态：1-上架，0-下架',
    create_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    update_time TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_category_id (category_id),
    INDEX idx_status (status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品表';
```

**步骤2：Entity类创建**
```java
@Entity
@Table(name = "goods")
public class Goods {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "name")
    private String name;
    
    @Column(name = "description")
    private String description;
    
    @Column(name = "price")
    private BigDecimal price;
    
    @Column(name = "stock")
    private Integer stock;
    
    @Column(name = "category_id")
    private Long categoryId;
    
    @Column(name = "status")
    private Integer status;
    
    @Column(name = "create_time")
    private Timestamp createTime;
    
    @Column(name = "update_time")
    private Timestamp updateTime;
    
    // getters and setters
}
```

**步骤3：Mapper接口实现**
```java
@Mapper
public interface GoodsMapper {
    
    @Select("SELECT * FROM goods WHERE id = #{id}")
    Goods selectById(Long id);
    
    @SelectProvider(type = GoodsSqlProvider.class, method = "selectByCondition")
    List<Goods> selectByCondition(GoodsQueryRequest request);
    
    @Insert("INSERT INTO goods (name, description, price, stock, category_id, status) " +
            "VALUES (#{name}, #{description}, #{price}, #{stock}, #{categoryId}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(Goods goods);
    
    @Update("UPDATE goods SET name = #{name}, description = #{description}, " +
            "price = #{price}, stock = #{stock}, category_id = #{categoryId}, status = #{status} " +
            "WHERE id = #{id}")
    int update(Goods goods);
    
    @Update("UPDATE goods SET stock = stock - #{quantity} WHERE id = #{id} AND stock >= #{quantity}")
    int reduceStock(@Param("id") Long id, @Param("quantity") Integer quantity);
    
    @Delete("DELETE FROM goods WHERE id = #{id}")
    int deleteById(Long id);
}
```

**步骤4：Service业务逻辑**
```java
@Service
public class GoodsService {
    
    @Autowired
    private GoodsMapper goodsMapper;
    
    @Transactional(rollbackFor = Exception.class)
    public Goods createGoods(GoodsCreateRequest request) {
        Goods goods = new Goods();
        goods.setName(request.getName());
        goods.setDescription(request.getDescription());
        goods.setPrice(request.getPrice());
        goods.setStock(request.getStock());
        goods.setCategoryId(request.getCategoryId());
        goods.setStatus(1); // 默认上架
        
        goodsMapper.insert(goods);
        return goods;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public Goods updateGoods(Long id, GoodsUpdateRequest request) {
        Goods goods = goodsMapper.selectById(id);
        if (goods == null) {
            throw new GoodsNotFoundException("商品不存在");
        }
        
        if (request.getName() != null) {
            goods.setName(request.getName());
        }
        if (request.getDescription() != null) {
            goods.setDescription(request.getDescription());
        }
        if (request.getPrice() != null) {
            goods.setPrice(request.getPrice());
        }
        if (request.getStock() != null) {
            goods.setStock(request.getStock());
        }
        if (request.getCategoryId() != null) {
            goods.setCategoryId(request.getCategoryId());
        }
        if (request.getStatus() != null) {
            goods.setStatus(request.getStatus());
        }
        
        goodsMapper.update(goods);
        return goods;
    }
    
    @Transactional(rollbackFor = Exception.class)
    public void reduceStock(Long goodsId, Integer quantity) {
        int result = goodsMapper.reduceStock(goodsId, quantity);
        if (result == 0) {
            throw new InsufficientStockException("库存不足");
        }
    }
    
    public Goods getGoods(Long id) {
        Goods goods = goodsMapper.selectById(id);
        if (goods == null) {
            throw new GoodsNotFoundException("商品不存在");
        }
        return goods;
    }
    
    public PageResult<Goods> queryGoods(GoodsQueryRequest request) {
        List<Goods> goods = goodsMapper.selectByCondition(request);
        long total = goodsMapper.countByCondition(request);
        return new PageResult<>(goods, total);
    }
}
```

**步骤5：Controller接口开发**
```java
@RestController
@RequestMapping("/api/goods")
public class GoodsController {
    
    @Autowired
    private GoodsService goodsService;
    
    @PostMapping
    public ResponseEntity<ApiResponse<Goods>> createGoods(@Valid @RequestBody GoodsCreateRequest request) {
        Goods goods = goodsService.createGoods(request);
        return ResponseEntity.ok(ApiResponse.success(goods));
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<Goods>> getGoods(@PathVariable Long id) {
        Goods goods = goodsService.getGoods(id);
        return ResponseEntity.ok(ApiResponse.success(goods));
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<Goods>> updateGoods(
            @PathVariable Long id, 
            @Valid @RequestBody GoodsUpdateRequest request) {
        Goods goods = goodsService.updateGoods(id, request);
        return ResponseEntity.ok(ApiResponse.success(goods));
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteGoods(@PathVariable Long id) {
        goodsService.deleteGoods(id);
        return ResponseEntity.ok(ApiResponse.success());
    }
    
    @GetMapping
    public ResponseEntity<ApiResponse<PageResult<Goods>>> queryGoods(GoodsQueryRequest request) {
        PageResult<Goods> result = goodsService.queryGoods(request);
        return ResponseEntity.ok(ApiResponse.success(result));
    }
    
    @PostMapping("/{id}/stock/reduce")
    public ResponseEntity<ApiResponse<Void>> reduceStock(
            @PathVariable Long id, 
            @RequestBody StockReduceRequest request) {
        goodsService.reduceStock(id, request.getQuantity());
        return ResponseEntity.ok(ApiResponse.success());
    }
}
```

**步骤6：单元测试编写**
```java
@SpringBootTest
@Transactional
public class GoodsServiceTest {
    
    @Autowired
    private GoodsService goodsService;
    
    @Test
    public void testCreateGoods() {
        GoodsCreateRequest request = new GoodsCreateRequest();
        request.setName("测试商品");
        request.setDescription("这是一个测试商品");
        request.setPrice(new BigDecimal("99.99"));
        request.setStock(100);
        request.setCategoryId(1L);
        
        Goods goods = goodsService.createGoods(request);
        
        assertNotNull(goods);
        assertNotNull(goods.getId());
        assertEquals("测试商品", goods.getName());
        assertEquals(new BigDecimal("99.99"), goods.getPrice());
        assertEquals(100, goods.getStock());
    }
    
    @Test
    public void testReduceStock() {
        // 先创建商品
        GoodsCreateRequest createRequest = new GoodsCreateRequest();
        createRequest.setName("测试商品");
        createRequest.setPrice(new BigDecimal("99.99"));
        createRequest.setStock(100);
        createRequest.setCategoryId(1L);
        
        Goods goods = goodsService.createGoods(createRequest);
        
        // 减少库存
        goodsService.reduceStock(goods.getId(), 10);
        
        // 验证库存
        Goods updatedGoods = goodsService.getGoods(goods.getId());
        assertEquals(90, updatedGoods.getStock());
    }
    
    @Test(expected = InsufficientStockException.class)
    public void testReduceStockInsufficient() {
        // 先创建商品
        GoodsCreateRequest createRequest = new GoodsCreateRequest();
        createRequest.setName("测试商品");
        createRequest.setPrice(new BigDecimal("99.99"));
        createRequest.setStock(10);
        createRequest.setCategoryId(1L);
        
        Goods goods = goodsService.createGoods(createRequest);
        
        // 尝试减少超过库存的数量
        goodsService.reduceStock(goods.getId(), 20);
    }
}
```

### 3. 微服务间通信（2-3天）

#### 学习服务调用

**步骤1：创建Feign客户端**
```java
@FeignClient(name = "hobbits-ms-goods", path = "/api/goods")
public interface GoodsClient {
    
    @GetMapping("/{id}")
    ResponseEntity<ApiResponse<Goods>> getGoods(@PathVariable Long id);
    
    @PostMapping("/batch")
    ResponseEntity<ApiResponse<List<Goods>>> batchGetGoods(@RequestBody List<Long> ids);
    
    @PostMapping("/{id}/stock/reduce")
    ResponseEntity<ApiResponse<Void>> reduceStock(
            @PathVariable Long id, 
            @RequestBody StockReduceRequest request);
}
```

**步骤2：在Service中使用Feign客户端**
```java
@Service
public class OrderService {
    
    @Autowired
    private OrderMapper orderMapper;
    
    @Autowired
    private OrderItemMapper orderItemMapper;
    
    @Autowired
    private GoodsClient goodsClient;
    
    @Transactional(rollbackFor = Exception.class)
    public Order createOrder(OrderCreateRequest request) {
        // 1. 调用商品服务获取商品信息
        ResponseEntity<ApiResponse<Goods>> response = goodsClient.getGoods(request.getGoodsId());
        if (!response.getStatusCode().is2xxSuccessful() || response.getBody().getData() == null) {
            throw new GoodsNotFoundException("商品不存在");
        }
        
        Goods goods = response.getBody().getData();
        
        // 2. 检查库存
        if (goods.getStock() < request.getQuantity()) {
            throw new InsufficientStockException("库存不足");
        }
        
        // 3. 创建订单
        Order order = new Order();
        order.setUserId(request.getUserId());
        order.setTotalAmount(goods.getPrice().multiply(new BigDecimal(request.getQuantity())));
        order.setStatus("PENDING");
        
        orderMapper.insert(order);
        
        // 4. 创建订单项
        OrderItem orderItem = new OrderItem();
        orderItem.setOrderId(order.getId());
        orderItem.setGoodsId(goods.getId());
        orderItem.setGoodsName(goods.getName());
        orderItem.setGoodsPrice(goods.getPrice());
        orderItem.setQuantity(request.getQuantity());
        orderItem.setTotalAmount(goods.getPrice().multiply(new BigDecimal(request.getQuantity())));
        
        orderItemMapper.insert(orderItem);
        
        // 5. 减少库存
        StockReduceRequest stockRequest = new StockReduceRequest();
        stockRequest.setQuantity(request.getQuantity());
        goodsClient.reduceStock(goods.getId(), stockRequest);
        
        return order;
    }
}
```

**步骤3：处理服务调用异常**
```java
@ControllerAdvice
public class GlobalExceptionHandler {
    
    @ExceptionHandler(FeignException.class)
    public ResponseEntity<ApiResponse<String>> handleFeignException(FeignException e) {
        log.error("服务调用失败", e);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ApiResponse.error("服务暂时不可用，请稍后再试"));
    }
    
    @ExceptionHandler(ServiceUnavailableException.class)
    public ResponseEntity<ApiResponse<String>> handleServiceUnavailableException(ServiceUnavailableException e) {
        log.error("服务不可用", e);
        return ResponseEntity.status(HttpStatus.SERVICE_UNAVAILABLE)
                .body(ApiResponse.error(e.getMessage()));
    }
}
```

**步骤4：配置熔断和降级**
```java
@FeignClient(name = "hobbits-ms-goods", 
             path = "/api/goods",
             fallback = GoodsClientFallback.class)
public interface GoodsClient {
    // ... 方法定义
}

@Component
public class GoodsClientFallback implements GoodsClient {
    
    @Override
    public ResponseEntity<ApiResponse<Goods>> getGoods(Long id) {
        return ResponseEntity.ok(ApiResponse.error("商品服务暂时不可用"));
    }
    
    @Override
    public ResponseEntity<ApiResponse<List<Goods>>> batchGetGoods(List<Long> ids) {
        return ResponseEntity.ok(ApiResponse.error("商品服务暂时不可用"));
    }
    
    @Override
    public ResponseEntity<ApiResponse<Void>> reduceStock(Long id, StockReduceRequest request) {
        return ResponseEntity.ok(ApiResponse.error("商品服务暂时不可用"));
    }
}
```

## 🐳 第四阶段：部署与运维（1周）

### 1. Docker容器化（2-3天）

#### Docker基础

**步骤1：了解Dockerfile**
```dockerfile
# 基础镜像
FROM openjdk:8-jre-alpine

# 设置工作目录
WORKDIR /app

# 复制jar包
COPY target/hobbits-ms-harbor-1.0.0.jar app.jar

# 设置JVM参数
ENV JAVA_OPTS="-Xmx512m -Xms512m"

# 暴露端口
EXPOSE 8080

# 启动命令
ENTRYPOINT ["sh", "-c", "java $JAVA_OPTS -jar app.jar"]
```

**步骤2：构建Docker镜像**
```bash
# 进入项目目录
cd hobbits-ms-harbor

# 构建项目
mvn clean package -DskipTests

# 构建Docker镜像
docker build -t hobbits-ms-harbor:1.0.0 .

# 查看镜像
docker images | grep hobbits-ms-harbor
```

**步骤3：运行Docker容器**
```bash
# 运行容器
docker run -d \
  --name hobbits-ms-harbor \
  -p 8080:8080 \
  -e SPRING_PROFILES_ACTIVE=dev \
  -e SPRING_DATASOURCE_URL=******************************************* \
  -e SPRING_REDIS_HOST=host.docker.internal \
  hobbits-ms-harbor:1.0.0

# 查看容器状态
docker ps | grep hobbits-ms-harbor

# 查看容器日志
docker logs -f hobbits-ms-harbor
```

**步骤4：Docker Compose编排**
```yaml
# docker-compose.yml
version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: djos
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - djos-network

  redis:
    image: redis:6.2
    container_name: redis
    ports:
      - "6379:6379"
    networks:
      - djos-network

  nacos:
    image: nacos/nacos-server:2.0.4
    container_name: nacos
    ports:
      - "8848:8848"
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: password
    networks:
      - djos-network

  harbor-service:
    build: ./hobbits-ms-harbor
    container_name: harbor-service
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: dev
      SPRING_DATASOURCE_URL: ****************************
      SPRING_REDIS_HOST: redis
      SPRING_CLOUD_NACOS_DISCOVERY_SERVER-ADDR: nacos:8848
    depends_on:
      - mysql
      - redis
      - nacos
    networks:
      - djos-network

networks:
  djos-network:
    driver: bridge

volumes:
  mysql_data:
```

### 2. 本地环境部署（2-3天）

#### 完整环境搭建

**步骤1：启动基础服务**
```bash
# 启动所有依赖服务
docker-compose up -d mysql redis nacos

# 等待服务启动完成
sleep 30

# 检查服务状态
docker-compose ps
```

**步骤2：初始化数据库**
```bash
# 创建数据库和表
docker exec -i mysql mysql -uroot -ppassword -e "CREATE DATABASE IF NOT EXISTS djos;"

# 导入数据结构
# 如果有SQL文件，可以执行：
# docker exec -i mysql mysql -uroot -ppassword djos < schema.sql
```

**步骤3：构建并运行应用**
```bash
# 构建所有服务
mvn clean package -DskipTests

# 构建Docker镜像
docker-compose build

# 启动应用服务
docker-compose up -d harbor-service

# 查看日志
docker-compose logs -f harbor-service
```

**步骤4：验证服务**
```bash
# 检查服务健康状态
curl http://localhost:8080/actuator/health

# 测试API接口
curl http://localhost:8080/api/harbor/demo/hello
```

**步骤5：常用运维命令**
```bash
# 查看所有容器状态
docker-compose ps

# 查看服务日志
docker-compose logs -f [service-name]

# 重启服务
docker-compose restart [service-name]

# 停止所有服务
docker-compose down

# 停止并删除所有容器和卷
docker-compose down -v

# 重新构建并启动
docker-compose up -d --build
```

## 🤖 第五阶段：AI辅助开发技巧（持续学习）

### 1. Cursor使用技巧

#### 代码生成

**需求分析转代码**
```
用户需求：创建一个用户注册接口，包含用户名、邮箱、密码字段
要求：邮箱格式验证，密码加密存储，返回用户信息（不含密码）
```

**使用Cursor生成代码**
```java
// 生成的DTO类
public class UserRegisterRequest {
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50之间")
    private String username;
    
    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "邮箱不能为空")
    private String email;
    
    @NotBlank(message = "密码不能为空")
    @Size(min = 6, max = 20, message = "密码长度必须在6-20之间")
    private String password;
    
    // getters and setters
}

// 生成的Controller
@RestController
@RequestMapping("/api/users")
public class UserController {
    
    @Autowired
    private UserService userService;
    
    @PostMapping("/register")
    public ResponseEntity<ApiResponse<UserDTO>> register(@Valid @RequestBody UserRegisterRequest request) {
        UserDTO user = userService.register(request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }
}
```

**基于现有代码风格生成**
```
分析现有代码风格，生成一个新的Controller，遵循相同的格式和命名规范
```

**代码重构和优化**
```
分析以下代码的性能问题，并提供优化建议：
[粘贴需要优化的代码]
```

#### 高效开发模式

**AI辅助工作流**

1. **需求分析 → 用AI生成技术方案**
   ```
   需求：实现一个商品搜索功能，支持关键词搜索、分类筛选、价格区间筛选
   要求：分页查询，性能优化，返回商品列表和总数
   ```

2. **代码编写 → 用AI生成基础代码框架**
   ```
   基于上面的需求，生成完整的Controller、Service、Mapper代码
   包含：参数验证、业务逻辑、SQL查询、异常处理
   ```

3. **代码审查 → 用AI检查代码质量**
   ```
   检查以下代码的问题：
   - 安全性漏洞
   - 性能问题
   - 代码规范
   - 最佳实践
   ```

4. **问题解决 → 用AI调试和修复bug**
   ```
   描述问题：商品搜索接口在大量数据时响应缓慢
   现有代码：[粘贴代码]
   请分析原因并提供解决方案
   ```

**常用AI提示词模板**

**代码生成模板**
```
请为DJOS项目生成一个[功能名称]的完整实现，包含：
1. Entity类（对应数据库表）
2. Mapper接口（使用MyBatis注解）
3. Service类（包含业务逻辑）
4. Controller类（RESTful API）
5. DTO类（请求和响应对象）
6. 单元测试

要求：
- 遵循项目现有的代码风格
- 包含完整的参数验证
- 实现事务管理
- 异常处理要完善
- 添加必要的注释
```

**代码优化模板**
```
请分析以下代码的性能问题和优化建议：

[粘贴需要优化的代码]

关注点：
1. SQL查询性能
2. 内存使用
3. 并发处理
4. 缓存使用
5. 代码结构

请提供：
1. 问题分析
2. 优化方案
3. 优化后的代码
```

**调试模板**
```
我遇到了一个bug，现象是：[描述bug现象]
相关代码：[粘贴相关代码]
错误日志：[粘贴错误日志]

请帮我：
1. 分析可能的原因
2. 提供调试步骤
3. 给出修复方案
```

**学习模板**
```
我是PHP开发，正在学习Java Spring Boot，请解释以下概念：
1. [Java/Spring概念]
2. 与PHP的对比
3. 实际应用场景
4. 代码示例

请用通俗易懂的语言解释，并提供实际的项目中的例子。
```

### 2. 高效开发实践

#### 版本控制与AI协作

**Commit消息规范**
```bash
# 使用AI生成规范的commit消息
git commit -m "feat: 添加用户注册功能

- 实现用户注册接口
- 添加邮箱验证功能
- 密码加密存储
- 返回用户信息（不含密码）

🤖 Generated with [Claude Code](https://claude.ai/code)

Co-Authored-By: Claude <<EMAIL>>"
```

**代码审查流程**
```bash
# 使用AI进行代码审查
git diff --cached | pbcopy  # 复制暂存区的代码变更
# 然后询问AI：请审查以下代码变更，检查是否有问题
```

#### 项目文档生成

**API文档生成**
```
请为以下Controller生成Swagger API文档：

[粘贴Controller代码]

要求：
1. 添加Swagger注解
2. 完善参数说明
3. 添加示例值
4. 错误码说明
```

**技术文档生成**
```
请为[功能名称]功能生成技术文档，包含：
1. 功能概述
2. 技术实现
3. 数据库设计
4. API接口
5. 部署说明
6. 注意事项
```

## 📚 推荐学习资源

### 必读文档

#### 项目内部文档
1. **docs/learning/** 目录下的所有文档
2. **CLAUDE.md** 项目配置和开发指南
3. **digios-master-data/** 数据库表结构定义

#### 官方文档
1. **Spring Boot官方文档**: https://spring.io/projects/spring-boot
2. **MyBatis官方文档**: https://mybatis.org/mybatis-3/
3. **Spring Cloud官方文档**: https://spring.io/projects/spring-cloud
4. **Docker官方文档**: https://docs.docker.com/

#### 在线课程
1. **Spring Boot快速入门**
2. **MyBatis从入门到精通**
3. **Spring Cloud微服务实战**
4. **Docker容器化技术**

### 实践项目建议

#### 从简单到复杂的学习路径

**第一阶段：基础CRUD（1-2周）**
- 选择hobbits-ms-harbor中的简单接口
- 实现基本的增删改查功能
- 理解三层架构和MyBatis使用

**第二阶段：业务逻辑（2-3周）**
- 选择商品管理模块
- 实现完整的业务功能
- 学习事务管理和异常处理

**第三阶段：微服务通信（2-3周）**
- 实现订单创建功能
- 学习服务间调用和数据一致性
- 掌握熔断和降级机制

**第四阶段：高级特性（2-3周）**
- 实现缓存策略
- 学习消息队列使用
- 掌握监控和日志

#### 参考现有代码

**代码模仿学习**
1. **Controller层**：参考现有Controller的写法
2. **Service层**：学习业务逻辑的实现方式
3. **Mapper层**：理解SQL查询的写法
4. **配置文件**：学习各种配置的使用

**代码风格统一**
- 命名规范：类名、方法名、变量名
- 注释规范：类注释、方法注释
- 异常处理：统一异常处理机制
- API格式：统一的响应格式

### 常用命令速查

#### Maven命令
```bash
# 构建项目
mvn clean install

# 跳过测试构建
mvn clean package -DskipTests

# 运行测试
mvn test

# 运行特定测试
mvn test -Dtest=UserServiceTest

# 查看依赖树
mvn dependency:tree

# 清理项目
mvn clean

# 编译项目
mvn compile
```

#### Docker命令
```bash
# 构建镜像
docker build -t image-name:tag .

# 运行容器
docker run -d --name container-name -p 8080:8080 image-name:tag

# 查看容器
docker ps

# 查看容器日志
docker logs -f container-name

# 进入容器
docker exec -it container-name bash

# 停止容器
docker stop container-name

# 删除容器
docker rm container-name

# 删除镜像
docker rmi image-name:tag
```

#### Docker Compose命令
```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f service-name

# 重启服务
docker-compose restart service-name

# 停止所有服务
docker-compose down

# 重新构建并启动
docker-compose up -d --build

# 查看配置
docker-compose config
```

#### Git命令
```bash
# 查看状态
git status

# 查看修改
git diff

# 添加文件
git add file.txt

# 提交更改
git commit -m "commit message"

# 推送到远程
git push origin branch-name

# 拉取远程更改
git pull origin branch-name

# 查看日志
git log --oneline -10

# 创建分支
git checkout -b feature-branch

# 切换分支
git checkout branch-name
```

## 🎯 学习里程碑

### 时间规划

**第1周：Java基础和环境搭建**
- [ ] 完成Java基础语法学习
- [ ] 搭建开发环境
- [ ] 运行第一个Spring Boot应用
- [ ] 理解基本的项目结构

**第2周：Spring Boot框架学习**
- [ ] 掌握依赖注入和AOP
- [ ] 学习数据库操作（MyBatis）
- [ ] 实现RESTful API
- [ ] 理解配置文件和环境管理

**第3-4周：DJOS项目架构理解**
- [ ] 理解微服务架构
- [ ] 学习各服务模块的功能
- [ ] 掌握项目代码结构
- [ ] 能够运行和调试项目

**第5-6周：实践开发**
- [ ] 完成第一个功能开发
- [ ] 掌握微服务间通信
- [ ] 学习测试和部署
- [ ] 能够独立开发功能

**第7-8周：高级特性**
- [ ] 学习缓存和消息队列
- [ ] 掌握监控和日志
- [ ] 理解性能优化
- [ ] 能够参与实际项目开发

### 技能评估

**初级阶段（1-2周）**
- 能够理解Java基础语法
- 能够搭建开发环境
- 能够运行现有项目
- 能够修改简单的bug

**中级阶段（3-4周）**
- 能够独立开发CRUD功能
- 理解微服务架构
- 能够编写单元测试
- 能够部署到测试环境

**高级阶段（5-8周）**
- 能够设计复杂的业务功能
- 掌握微服务间通信
- 能够进行性能优化
- 能够参与生产环境开发

### 持续学习建议

1. **定期代码审查**：每周进行一次代码审查，学习优秀的代码实现
2. **技术分享**：定期学习新的技术，并在团队内分享
3. **项目实践**：积极参与实际项目开发，在实践中学习
4. **社区参与**：参与开源项目，学习他人的最佳实践
5. **文档编写**：编写技术文档，加深对技术的理解

## 🎉 总结

这个学习计划为你提供了从PHP转向Java开发的完整路径。通过系统性的学习和实践，你将能够：

1. **掌握Java和Spring Boot**：从基础语法到高级特性
2. **理解微服务架构**：掌握DJOS项目的架构设计
3. **具备实际开发能力**：能够独立开发和部署微服务
4. **善用AI工具**：提高开发效率和代码质量

记住，学习是一个持续的过程，最重要的是保持学习的热情和耐心。祝你学习顺利！