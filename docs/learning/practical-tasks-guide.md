# 实践任务指南

## 🎯 实践任务概述

本指南提供了DJOS项目的具体实践任务，帮助你从PHP开发者逐步成长为Java微服务开发者。每个任务都有明确的目标、步骤和验收标准。

## 📋 任务规划

### 任务级别划分
- **初级任务**：基础CRUD操作，适合第1-2周
- **中级任务**：业务逻辑实现，适合第3-4周
- **高级任务**：微服务通信和高级特性，适合第5-6周
- **专家任务**：性能优化和架构设计，适合第7-8周

### 学习路径
1. **环境熟悉** → 2. **基础CRUD** → 3. **业务功能** → 4. **微服务通信** → 5. **高级特性** → 6. **性能优化**

## 🏃‍♂️ 初级任务（第1-2周）

### 任务1：Hello World接口开发

#### 任务目标
- 熟悉项目结构
- 掌握基本的Spring Boot开发流程
- 能够创建简单的REST API

#### 任务描述
在 `hobbits-ms-harbor` 服务中创建一个简单的Hello World接口。

#### 实施步骤

**步骤1：熟悉项目结构**
```bash
# 进入项目目录
cd hobbits-ms-harbor

# 查看项目结构
find . -name "*.java" -type f | head -20

# 查看配置文件
cat src/main/resources/application.yml
```

**步骤2：创建Controller**
```java
// 在 platform/controller 目录下创建 DemoController.java
package cn.shopex.hobbits.harbor.platform.controller;

import cn.shopex.hobbits.common.response.ApiResponse;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/demo")
public class DemoController {

    @GetMapping("/hello")
    public ResponseEntity<ApiResponse<String>> hello() {
        return ResponseEntity.ok(ApiResponse.success("Hello, DJOS!"));
    }

    @GetMapping("/hello/{name}")
    public ResponseEntity<ApiResponse<String>> helloWithName(@PathVariable String name) {
        String message = String.format("Hello, %s! Welcome to DJOS!", name);
        return ResponseEntity.ok(ApiResponse.success(message));
    }

    @PostMapping("/echo")
    public ResponseEntity<ApiResponse<String>> echo(@RequestBody EchoRequest request) {
        return ResponseEntity.ok(ApiResponse.success(request.getMessage()));
    }
}

// 请求对象
class EchoRequest {
    private String message;

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }
}
```

**步骤3：创建测试用例**
```java
// 在 src/test/java 目录下创建 DemoControllerTest.java
package cn.shopex.hobbits.harbor.platform.controller;

import cn.shopex.hobbits.common.response.ApiResponse;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.MvcResult;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@SpringBootTest
@AutoConfigureMockMvc
public class DemoControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    public void testHello() throws Exception {
        mockMvc.perform(get("/api/demo/hello"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("Hello, DJOS!"));
    }

    @Test
    public void testHelloWithName() throws Exception {
        mockMvc.perform(get("/api/demo/hello/张三"))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("Hello, 张三! Welcome to DJOS!"));
    }

    @Test
    public void testEcho() throws Exception {
        String requestJson = "{\"message\":\"Hello from test\"}";

        mockMvc.perform(post("/api/demo/echo")
                .contentType(MediaType.APPLICATION_JSON)
                .content(requestJson))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(200))
                .andExpect(jsonPath("$.data").value("Hello from test"));
    }
}
```

**步骤4：运行和测试**
```bash
# 进入项目目录
cd hobbits-ms-harbor

# 运行测试
mvn test -Dtest=DemoControllerTest

# 启动服务
mvn spring-boot:run

# 测试接口
curl http://localhost:8080/api/demo/hello
curl http://localhost:8080/api/demo/hello/张三
curl -X POST -H "Content-Type: application/json" -d '{"message":"Hello"}' http://localhost:8080/api/demo/echo
```

#### 验收标准
- [ ] 服务能够正常启动
- [ ] 所有测试用例通过
- [ ] 接口返回正确的响应格式
- [ ] 代码符合项目规范

### 任务2：数据库CRUD操作

#### 任务目标
- 掌握MyBatis基本操作
- 理解三层架构
- 能够实现完整的CRUD功能

#### 任务描述
在 `hobbits-ms-harbor` 服务中创建一个用户管理功能，包含增删改查操作。

#### 实施步骤

**步骤1：创建数据库表**
```sql
-- 在MySQL中创建用户表
CREATE TABLE IF NOT EXISTS `demo_user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT,
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `email` varchar(100) NOT NULL COMMENT '邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
    `create_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP,
    `update_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='演示用户表';

-- 插入测试数据
INSERT INTO `demo_user` (`username`, `email`, `phone`, `status`) VALUES
('admin', '<EMAIL>', '13800138000', 1),
('user1', '<EMAIL>', '13800138001', 1),
('user2', '<EMAIL>', '13800138002', 1);
```

**步骤2：创建Entity类**
```java
// 在 orm/model 目录下创建 DemoUser.java
package cn.shopex.hobbits.harbor.orm.model;

import javax.persistence.*;
import java.sql.Timestamp;

@Entity
@Table(name = "demo_user")
public class DemoUser {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    @Column(name = "username")
    private String username;

    @Column(name = "email")
    private String email;

    @Column(name = "phone")
    private String phone;

    @Column(name = "status")
    private Integer status;

    @Column(name = "create_time")
    private Timestamp createTime;

    @Column(name = "update_time")
    private Timestamp updateTime;

    // Getters and Setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Timestamp getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Timestamp createTime) {
        this.createTime = createTime;
    }

    public Timestamp getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Timestamp updateTime) {
        this.updateTime = updateTime;
    }
}
```

**步骤3：创建Mapper接口**
```java
// 在 orm/mapper 目录下创建 DemoUserMapper.java
package cn.shopex.hobbits.harbor.orm.mapper;

import cn.shopex.hobbits.harbor.orm.model.DemoUser;
import org.apache.ibatis.annotations.*;
import java.util.List;

@Mapper
public interface DemoUserMapper {

    @Select("SELECT * FROM demo_user WHERE id = #{id}")
    DemoUser selectById(Long id);

    @Select("SELECT * FROM demo_user WHERE username = #{username}")
    DemoUser selectByUsername(String username);

    @Select("SELECT * FROM demo_user WHERE email = #{email}")
    DemoUser selectByEmail(String email);

    @Select("SELECT * FROM demo_user WHERE status = #{status}")
    List<DemoUser> selectByStatus(Integer status);

    @Select("SELECT * FROM demo_user")
    List<DemoUser> selectAll();

    @Insert("INSERT INTO demo_user (username, email, phone, status) " +
            "VALUES (#{username}, #{email}, #{phone}, #{status})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(DemoUser user);

    @Update("UPDATE demo_user SET username = #{username}, email = #{email}, " +
            "phone = #{phone}, status = #{status} WHERE id = #{id}")
    int update(DemoUser user);

    @Update("UPDATE demo_user SET status = #{status} WHERE id = #{id}")
    int updateStatus(@Param("id") Long id, @Param("status") Integer status);

    @Delete("DELETE FROM demo_user WHERE id = #{id}")
    int deleteById(Long id);

    @Select("SELECT COUNT(*) FROM demo_user")
    long countAll();
}
```

**步骤4：创建DTO类**
```java
// 在 library/dto 目录下创建相关DTO
package cn.shopex.hobbits.harbor.library.dto;

import javax.validation.constraints.*;

public class UserCreateRequest {
    @NotBlank(message = "用户名不能为空")
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50之间")
    private String username;

    @Email(message = "邮箱格式不正确")
    @NotBlank(message = "邮箱不能为空")
    private String email;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    private Integer status = 1;

    // Getters and Setters
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

public class UserUpdateRequest {
    @Size(min = 3, max = 50, message = "用户名长度必须在3-50之间")
    private String username;

    @Email(message = "邮箱格式不正确")
    private String email;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;

    private Integer status;

    // Getters and Setters
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}

public class UserQueryRequest {
    private String username;
    private String email;
    private Integer status;
    private Integer page = 1;
    private Integer size = 10;

    // Getters and Setters
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getPage() {
        return page;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getSize() {
        return size;
    }

    public void setSize(Integer size) {
        this.size = size;
    }
}
```

**步骤5：创建Service类**
```java
// 在 platform/biz 目录下创建 DemoUserService.java
package cn.shopex.hobbits.harbor.platform.biz;

import cn.shopex.hobbits.harbor.library.dto.*;
import cn.shopex.hobbits.harbor.orm.mapper.DemoUserMapper;
import cn.shopex.hobbits.harbor.orm.model.DemoUser;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Service
public class DemoUserService {

    @Autowired
    private DemoUserMapper userMapper;

    @Transactional(rollbackFor = Exception.class)
    public DemoUser createUser(UserCreateRequest request) {
        // 检查用户名是否已存在
        if (userMapper.selectByUsername(request.getUsername()) != null) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userMapper.selectByEmail(request.getEmail()) != null) {
            throw new RuntimeException("邮箱已存在");
        }

        // 创建用户
        DemoUser user = new DemoUser();
        user.setUsername(request.getUsername());
        user.setEmail(request.getEmail());
        user.setPhone(request.getPhone());
        user.setStatus(request.getStatus());

        userMapper.insert(user);
        return user;
    }

    public DemoUser getUserById(Long id) {
        DemoUser user = userMapper.selectById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        return user;
    }

    @Transactional(rollbackFor = Exception.class)
    public DemoUser updateUser(Long id, UserUpdateRequest request) {
        DemoUser user = userMapper.selectById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }

        // 检查用户名是否已存在
        if (request.getUsername() != null && !request.getUsername().equals(user.getUsername())) {
            if (userMapper.selectByUsername(request.getUsername()) != null) {
                throw new RuntimeException("用户名已存在");
            }
            user.setUsername(request.getUsername());
        }

        // 检查邮箱是否已存在
        if (request.getEmail() != null && !request.getEmail().equals(user.getEmail())) {
            if (userMapper.selectByEmail(request.getEmail()) != null) {
                throw new RuntimeException("邮箱已存在");
            }
            user.setEmail(request.getEmail());
        }

        if (request.getPhone() != null) {
            user.setPhone(request.getPhone());
        }

        if (request.getStatus() != null) {
            user.setStatus(request.getStatus());
        }

        userMapper.update(user);
        return user;
    }

    @Transactional(rollbackFor = Exception.class)
    public void deleteUser(Long id) {
        DemoUser user = userMapper.selectById(id);
        if (user == null) {
            throw new RuntimeException("用户不存在");
        }
        userMapper.deleteById(id);
    }

    public List<DemoUser> queryUsers(UserQueryRequest request) {
        // 这里简化处理，实际应该使用动态SQL
        if (request.getStatus() != null) {
            return userMapper.selectByStatus(request.getStatus());
        }
        return userMapper.selectAll();
    }

    public DemoUser getUserByUsername(String username) {
        return userMapper.selectByUsername(username);
    }

    public DemoUser getUserByEmail(String email) {
        return userMapper.selectByEmail(email);
    }
}
```

**步骤6：创建Controller**
```java
// 在 platform/controller 目录下创建 DemoUserController.java
package cn.shopex.hobbits.harbor.platform.controller;

import cn.shopex.hobbits.common.response.ApiResponse;
import cn.shopex.hobbits.harbor.library.dto.*;
import cn.shopex.hobbits.harbor.orm.model.DemoUser;
import cn.shopex.hobbits.harbor.platform.biz.DemoUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("/api/demo/users")
public class DemoUserController {

    @Autowired
    private DemoUserService userService;

    @PostMapping
    public ResponseEntity<ApiResponse<DemoUser>> createUser(@Valid @RequestBody UserCreateRequest request) {
        DemoUser user = userService.createUser(request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @GetMapping("/{id}")
    public ResponseEntity<ApiResponse<DemoUser>> getUserById(@PathVariable Long id) {
        DemoUser user = userService.getUserById(id);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @GetMapping("/username/{username}")
    public ResponseEntity<ApiResponse<DemoUser>> getUserByUsername(@PathVariable String username) {
        DemoUser user = userService.getUserByUsername(username);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @GetMapping("/email/{email}")
    public ResponseEntity<ApiResponse<DemoUser>> getUserByEmail(@PathVariable String email) {
        DemoUser user = userService.getUserByEmail(email);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @GetMapping
    public ResponseEntity<ApiResponse<List<DemoUser>>> queryUsers(UserQueryRequest request) {
        List<DemoUser> users = userService.queryUsers(request);
        return ResponseEntity.ok(ApiResponse.success(users));
    }

    @PutMapping("/{id}")
    public ResponseEntity<ApiResponse<DemoUser>> updateUser(
            @PathVariable Long id,
            @Valid @RequestBody UserUpdateRequest request) {
        DemoUser user = userService.updateUser(id, request);
        return ResponseEntity.ok(ApiResponse.success(user));
    }

    @DeleteMapping("/{id}")
    public ResponseEntity<ApiResponse<Void>> deleteUser(@PathVariable Long id) {
        userService.deleteUser(id);
        return ResponseEntity.ok(ApiResponse.success());
    }
}
```

**步骤7：创建测试用例**
```java
// 在 src/test/java 目录下创建 DemoUserServiceTest.java
package cn.shopex.hobbits.harbor.platform.biz;

import cn.shopex.hobbits.harbor.library.dto.UserCreateRequest;
import cn.shopex.hobbits.harbor.library.dto.UserUpdateRequest;
import cn.shopex.hobbits.harbor.orm.model.DemoUser;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.transaction.annotation.Transactional;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
@Transactional
public class DemoUserServiceTest {

    @Autowired
    private DemoUserService userService;

    @Test
    public void testCreateUser() {
        UserCreateRequest request = new UserCreateRequest();
        request.setUsername("testuser");
        request.setEmail("<EMAIL>");
        request.setPhone("13800138000");
        request.setStatus(1);

        DemoUser user = userService.createUser(request);

        assertNotNull(user);
        assertNotNull(user.getId());
        assertEquals("testuser", user.getUsername());
        assertEquals("<EMAIL>", user.getEmail());
        assertEquals("13800138000", user.getPhone());
        assertEquals(1, user.getStatus());
    }

    @Test
    public void testCreateUserWithDuplicateUsername() {
        // 创建第一个用户
        UserCreateRequest request1 = new UserCreateRequest();
        request1.setUsername("duplicate");
        request1.setEmail("<EMAIL>");
        userService.createUser(request1);

        // 尝试创建相同用户名的用户
        UserCreateRequest request2 = new UserCreateRequest();
        request2.setUsername("duplicate");
        request2.setEmail("<EMAIL>");

        assertThrows(RuntimeException.class, () -> {
            userService.createUser(request2);
        });
    }

    @Test
    public void testUpdateUser() {
        // 先创建用户
        UserCreateRequest createRequest = new UserCreateRequest();
        createRequest.setUsername("original");
        createRequest.setEmail("<EMAIL>");
        DemoUser user = userService.createUser(createRequest);

        // 更新用户
        UserUpdateRequest updateRequest = new UserUpdateRequest();
        updateRequest.setUsername("updated");
        updateRequest.setEmail("<EMAIL>");
        updateRequest.setPhone("13900139000");
        updateRequest.setStatus(0);

        DemoUser updatedUser = userService.updateUser(user.getId(), updateRequest);

        assertEquals("updated", updatedUser.getUsername());
        assertEquals("<EMAIL>", updatedUser.getEmail());
        assertEquals("13900139000", updatedUser.getPhone());
        assertEquals(0, updatedUser.getStatus());
    }

    @Test
    public void testDeleteUser() {
        // 先创建用户
        UserCreateRequest request = new UserCreateRequest();
        request.setUsername("todelete");
        request.setEmail("<EMAIL>");
        DemoUser user = userService.createUser(request);

        // 删除用户
        userService.deleteUser(user.getId());

        // 验证用户已被删除
        assertThrows(RuntimeException.class, () -> {
            userService.getUserById(user.getId());
        });
    }
}
```

**步骤8：运行和测试**
```bash
# 进入项目目录
cd hobbits-ms-harbor

# 运行测试
mvn test -Dtest=DemoUserServiceTest

# 启动服务
mvn spring-boot:run

# 测试接口
curl -X POST -H "Content-Type: application/json" -d '{"username":"test","email":"<EMAIL>","phone":"13800138000"}' http://localhost:8080/api/demo/users
curl http://localhost:8080/api/demo/users/1
curl http://localhost:8080/api/demo/users/username/test
curl -X PUT -H "Content-Type: application/json" -d '{"phone":"13900139000"}' http://localhost:8080/api/demo/users/1
curl -X DELETE http://localhost:8080/api/demo/users/1
```

#### 验收标准
- [ ] 所有测试用例通过
- [ ] CRUD功能正常工作
- [ ] 数据验证正确
- [ ] 异常处理完善
- [ ] 代码符合项目规范

## 🏃‍♂️ 中级任务（第3-4周）

### 任务3：商品管理功能

#### 任务目标
- 掌握复杂业务逻辑实现
- 学习事务管理
- 理解关联查询

#### 任务描述
在 `hobbits-ms-goods` 服务中实现商品管理功能，包含商品分类、库存管理等。

#### 实施步骤

**步骤1：分析现有商品模块**
```bash
# 查看商品模块结构
cd hobbits-ms-goods
find . -name "*.java" -type f | grep -E "(goods|category)" | head -10

# 查看数据库表定义
# 在 digios-master-data/2.24.0/ 目录下查看 goods_*.yaml 文件
```

**步骤2：实现商品分类管理**
```java
// 创建分类管理功能
// 1. 创建分类Entity
// 2. 创建分类Mapper
// 3. 创建分类Service
// 4. 创建分类Controller
// 5. 实现分类的CRUD操作
```

**步骤3：实现商品管理**
```java
// 创建商品管理功能
// 1. 创建商品Entity（关联分类）
// 2. 创建商品Mapper（包含复杂查询）
// 3. 创建商品Service（包含业务逻辑）
// 4. 创建商品Controller
// 5. 实现商品的CRUD和查询功能
```

**步骤4：实现库存管理**
```java
// 创建库存管理功能
// 1. 创建库存Entity
// 2. 创建库存Service
// 3. 实现库存增减功能
// 4. 实现库存检查功能
```

**步骤5：添加事务管理**
```java
// 在Service方法上添加@Transactional注解
// 确保数据一致性
```

#### 验收标准
- [ ] 商品分类管理功能完整
- [ ] 商品管理功能完整
- [ ] 库存管理功能完整
- [ ] 事务管理正确
- [ ] 性能测试通过

### 任务4：订单管理功能

#### 任务目标
- 掌握复杂业务流程
- 学习微服务间通信
- 理解分布式事务

#### 任务描述
在 `hobbits-ms-order` 服务中实现订单管理功能，包含订单创建、支付、发货等流程。

#### 实施步骤

**步骤1：分析订单模块**
```bash
# 查看订单模块结构
cd hobbits-ms-order
find . -name "*.java" -type f | grep -E "(order|payment)" | head -10

# 查看数据库表定义
# 在 digios-master-data/2.24.0/ 目录下查看 order_*.yaml 文件
```

**步骤2：实现订单创建**
```java
// 创建订单创建功能
// 1. 创建订单Entity
// 2. 创建订单Item Entity
// 3. 创建订单Service
// 4. 实现订单创建逻辑
// 5. 调用商品服务检查库存
```

**步骤3：实现支付功能**
```java
// 创建支付功能
// 1. 创建支付Entity
// 2. 创建支付Service
// 3. 实现支付状态管理
// 4. 实现支付回调处理
```

**步骤4：实现发货功能**
```java
// 创建发货功能
// 1. 创建发货Entity
// 2. 创建发货Service
// 3. 实现发货状态管理
// 4. 实现物流信息管理
```

**步骤5：实现服务间通信**
```java
// 使用OpenFeign调用其他服务
// 1. 创建GoodsClient
// 2. 创建UserClient
// 3. 实现服务调用
// 4. 添加熔断和降级
```

#### 验收标准
- [ ] 订单创建功能完整
- [ ] 支付功能完整
- [ ] 发货功能完整
- [ ] 服务间通信正常
- [ ] 分布式事务处理正确

## 🏃‍♂️ 高级任务（第5-6周）

### 任务5：缓存优化

#### 任务目标
- 掌握Redis缓存使用
- 学习缓存策略
- 理解缓存穿透和雪崩

#### 任务描述
为商品管理功能添加缓存优化，提高查询性能。

#### 实施步骤

**步骤1：添加Redis配置**
```java
// 在application.yml中添加Redis配置
spring:
  redis:
    host: localhost
    port: 6379
    database: 0
    timeout: 10000ms
    lettuce:
      pool:
        max-active: 8
        max-wait: -1ms
        max-idle: 8
        min-idle: 0
```

**步骤2：实现商品缓存**
```java
// 创建商品缓存Service
@Service
public class GoodsCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Autowired
    private GoodsService goodsService;
    
    public Goods getGoodsFromCache(Long goodsId) {
        String key = "goods:" + goodsId;
        Goods goods = (Goods) redisTemplate.opsForValue().get(key);
        
        if (goods == null) {
            goods = goodsService.getGoodsById(goodsId);
            if (goods != null) {
                redisTemplate.opsForValue().set(key, goods, 1, TimeUnit.HOURS);
            }
        }
        
        return goods;
    }
    
    public void updateGoodsCache(Long goodsId, Goods goods) {
        String key = "goods:" + goodsId;
        redisTemplate.opsForValue().set(key, goods, 1, TimeUnit.HOURS);
    }
    
    public void deleteGoodsCache(Long goodsId) {
        String key = "goods:" + goodsId;
        redisTemplate.delete(key);
    }
}
```

**步骤3：实现缓存策略**
```java
// 实现缓存穿透防护
public Goods getGoodsWithProtection(Long goodsId) {
    String key = "goods:" + goodsId;
    String cacheKey = "goods:cache:protection:" + goodsId;
    
    // 检查缓存保护
    if (redisTemplate.hasKey(cacheKey)) {
        return null; // 缓存穿透保护
    }
    
    Goods goods = (Goods) redisTemplate.opsForValue().get(key);
    
    if (goods == null) {
        goods = goodsService.getGoodsById(goodsId);
        
        if (goods == null) {
            // 设置缓存保护，防止缓存穿透
            redisTemplate.opsForValue().set(cacheKey, "NULL", 5, TimeUnit.MINUTES);
        } else {
            redisTemplate.opsForValue().set(key, goods, 1, TimeUnit.HOURS);
        }
    }
    
    return goods;
}
```

**步骤4：实现缓存雪崩防护**
```java
// 实现缓存雪崩防护
@PostConstruct
public void initCache() {
    // 预热热门商品缓存
    List<Goods> hotGoods = goodsService.getHotGoods();
    for (Goods goods : hotGoods) {
        // 设置不同的过期时间，防止雪崩
        long expireTime = 30 + (long) (Math.random() * 30);
        redisTemplate.opsForValue().set("goods:" + goods.getId(), goods, expireTime, TimeUnit.MINUTES);
    }
}
```

#### 验收标准
- [ ] 缓存功能正常工作
- [ ] 缓存穿透防护有效
- [ ] 缓存雪崩防护有效
- [ ] 性能测试通过
- [ ] 缓存一致性保证

### 任务6：消息队列集成

#### 任务目标
- 掌握RocketMQ使用
- 学习异步处理
- 理解消息可靠性

#### 任务描述
为订单创建功能添加消息队列，实现异步处理。

#### 实施步骤

**步骤1：添加RocketMQ配置**
```yaml
# 在application.yml中添加RocketMQ配置
rocketmq:
  name-server: localhost:9876
  producer:
    group: order-producer-group
    send-message-timeout: 3000
    retry-times-when-send-failed: 3
```

**步骤2：创建消息生产者**
```java
@Component
public class OrderMessageProducer {
    
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    
    public void sendOrderCreatedMessage(Order order) {
        // 发送订单创建消息
        rocketMQTemplate.convertAndSend("order-created-topic", order);
    }
    
    public void sendOrderPaidMessage(Order order) {
        // 发送订单支付消息
        rocketMQTemplate.convertAndSend("order-paid-topic", order);
    }
    
    public void sendOrderShippedMessage(Order order) {
        // 发送订单发货消息
        rocketMQTemplate.convertAndSend("order-shipped-topic", order);
    }
}
```

**步骤3：创建消息消费者**
```java
@Component
@RocketMQMessageListener(topic = "order-created-topic", consumerGroup = "order-consumer-group")
public class OrderCreatedConsumer implements RocketMQListener<Order> {
    
    @Autowired
    private InventoryService inventoryService;
    
    @Autowired
    private LogService logService;
    
    @Override
    public void onMessage(Order order) {
        try {
            // 处理订单创建消息
            logService.info("收到订单创建消息，订单ID: " + order.getId());
            
            // 扣减库存
            inventoryService.reduceInventory(order.getGoodsId(), order.getQuantity());
            
            logService.info("订单创建消息处理完成，订单ID: " + order.getId());
        } catch (Exception e) {
            logService.error("处理订单创建消息失败，订单ID: " + order.getId(), e);
            // 消息重试机制会自动处理
        }
    }
}
```

**步骤4：实现消息可靠性**
```java
// 实现消息重试机制
@Component
@RocketMQMessageListener(
    topic = "order-created-topic",
    consumerGroup = "order-consumer-group",
    maxReconsumeTimes = 3
)
public class OrderCreatedConsumer implements RocketMQListener<Order> {
    
    @Override
    public void onMessage(Order order) {
        try {
            // 处理消息
            processOrderCreated(order);
        } catch (Exception e) {
            // 记录错误日志
            log.error("处理订单创建消息失败", e);
            // 抛出异常触发重试
            throw new RuntimeException("处理订单创建消息失败", e);
        }
    }
    
    private void processOrderCreated(Order order) {
        // 具体的消息处理逻辑
    }
}
```

#### 验收标准
- [ ] 消息发送功能正常
- [ ] 消息消费功能正常
- [ ] 消息重试机制有效
- [ ] 消息可靠性保证
- [ ] 性能测试通过

## 🏃‍♂️ 专家任务（第7-8周）

### 任务7：性能优化

#### 任务目标
- 掌握性能调优技巧
- 学习监控和日志
- 理解JVM优化

#### 任务描述
对订单创建功能进行性能优化，提高系统吞吐量。

#### 实施步骤

**步骤1：性能分析**
```java
// 使用Spring Boot Actuator进行性能监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
```

**步骤2：SQL优化**
```java
// 优化数据库查询
@Select("SELECT o.*, u.username, g.name as goods_name " +
        "FROM orders o " +
        "LEFT JOIN users u ON o.user_id = u.id " +
        "LEFT JOIN goods g ON o.goods_id = g.id " +
        "WHERE o.id = #{id}")
Order selectOrderWithDetails(Long id);
```

**步骤3：JVM优化**
```bash
# 在启动脚本中添加JVM参数
JAVA_OPTS="-Xmx2g -Xms2g -XX:+UseG1GC -XX:MaxGCPauseMillis=200"
```

**步骤4：缓存优化**
```java
// 使用多级缓存
@Service
public class OrderCacheService {
    
    @Autowired
    private CacheManager cacheManager;
    
    public Order getOrderWithCache(Long orderId) {
        // 一级缓存：本地缓存
        Cache localCache = cacheManager.getCache("local-order-cache");
        Order order = localCache.get(orderId, Order.class);
        
        if (order == null) {
            // 二级缓存：Redis缓存
            order = getFromRedisCache(orderId);
            
            if (order == null) {
                // 三级缓存：数据库
                order = orderService.getOrderById(orderId);
                
                if (order != null) {
                    // 缓存到Redis
                    setToRedisCache(orderId, order);
                    // 缓存到本地
                    localCache.put(orderId, order);
                }
            }
        }
        
        return order;
    }
}
```

#### 验收标准
- [ ] 性能指标达到预期
- [ ] 系统稳定性良好
- [ ] 资源使用合理
- [ ] 监控指标正常
- [ ] 用户体验良好

### 任务8：架构设计

#### 任务目标
- 掌握微服务架构设计
- 学习分布式系统设计
- 理解高可用设计

#### 任务描述
设计一个新的微服务模块，如推荐服务，包含完整的架构设计。

#### 实施步骤

**步骤1：需求分析**
```java
// 分析推荐服务需求
// 1. 用户行为收集
// 2. 推荐算法实现
// 3. 推荐结果缓存
// 4. 推荐结果展示
```

**步骤2：架构设计**
```java
// 设计推荐服务架构
// 1. 数据收集层
// 2. 算法处理层
// 3. 缓存层
// 4. API层
```

**步骤3：技术选型**
```java
// 选择合适的技术栈
// 1. Spring Boot + Spring Cloud
// 2. Redis + MongoDB
// 3. RocketMQ
// 4. Elasticsearch
```

**步骤4：实现核心功能**
```java
// 实现推荐算法
@Service
public class RecommendationService {
    
    @Autowired
    private UserBehaviorService userBehaviorService;
    
    @Autowired
    private GoodsService goodsService;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    public List<Goods> getRecommendations(Long userId) {
        String cacheKey = "recommendations:" + userId;
        
        // 检查缓存
        List<Goods> recommendations = (List<Goods>) redisTemplate.opsForValue().get(cacheKey);
        
        if (recommendations == null) {
            // 基于用户行为计算推荐
            recommendations = calculateRecommendations(userId);
            
            // 缓存推荐结果
            redisTemplate.opsForValue().set(cacheKey, recommendations, 1, TimeUnit.HOURS);
        }
        
        return recommendations;
    }
    
    private List<Goods> calculateRecommendations(Long userId) {
        // 实现推荐算法
        // 1. 收集用户行为
        // 2. 分析用户偏好
        // 3. 计算相似用户
        // 4. 生成推荐列表
        return new ArrayList<>();
    }
}
```

#### 验收标准
- [ ] 架构设计合理
- [ ] 技术选型合适
- [ ] 核心功能完整
- [ ] 性能测试通过
- [ ] 可扩展性良好

## 📊 任务完成评估

### 评估维度

#### 代码质量
- [ ] 代码结构清晰
- [ ] 命名规范统一
- [ ] 注释完整
- [ ] 测试覆盖率高

#### 功能完整性
- [ ] 需求功能完整实现
- [ ] 边界情况处理
- [ ] 异常处理完善
- [ ] 用户体验良好

#### 性能指标
- [ ] 响应时间达标
- [ ] 吞吐量达标
- [ ] 资源使用合理
- [ ] 系统稳定性好

#### 可维护性
- [ ] 代码可读性好
- [ ] 模块化程度高
- [ ] 配置管理规范
- [ ] 文档完整

### 学习成果展示

#### 代码仓库
- 将完成的代码提交到Git仓库
- 创建分支管理不同任务
- 编写清晰的commit message

#### 技术文档
- 编写API文档
- 编写部署文档
- 编写使用说明

#### 演示展示
- 准备功能演示
- 性能测试报告
- 代码review展示

## 🎯 总结

通过这些实践任务，你将逐步掌握：

1. **Java基础**：语法、面向对象、集合框架
2. **Spring Boot**：自动配置、依赖注入、REST API
3. **数据库**：MyBatis、事务管理、性能优化
4. **微服务**：服务通信、分布式事务、高可用
5. **中间件**：Redis、RocketMQ、Nacos
6. **DevOps**：Docker、监控、日志

每个任务都有明确的目标和验收标准，确保你能够循序渐进地掌握Java微服务开发技能。祝学习顺利！