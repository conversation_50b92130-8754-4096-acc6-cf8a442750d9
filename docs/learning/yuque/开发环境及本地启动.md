## 工具安装及下载
### 安装JDK
JDK版本 >= 1.8

环境变量设置

### 安装 GIT （可选）
根据版本工具选择，目前基本都是 GIT

### 安装MySQL GUI 工具
Navicat或者WorkBench

### 安装 Redis 客户端工具
Another Redis

### maven设置
修改.m2文件夹下setting.xml

[settings.xml](https://shopex.yuque.com/attachments/yuque/0/2024/xml/21844049/1714293932547-fd9c2476-7857-4568-9b29-c28fabf4027f.xml)

### 获取 IDEA
官网下载安装包

安装插件

Alibaba Java Coding Guidelines

Git Commit Template

Grep Console

Free Mybatis plugins

Lomok（新版本已经内置）

## 服务环境
### MySQL
+ 构建本地环境
+ 版本 5.7 +
+ 导入脚本
    - 建库
    - 基础数据
    - 脚本数据见 Git 仓库

### Redis 
+ 构建本地环境
+ 版本 5.0+

### MQ
#### RocketMQ
+ RocketMQ 构建本地环境
+ 手动建 Topic
+ 具体配置说明确认中

#### RabbitMQ
+ 和 RocketMQ 2选一

### ES
+ 可选

## 本地启动
### 使用 IDEA 加载本地工程
#### 构建最小启动环境，mgr + base
+ 首先导入mgr工程
+ 打开 IDEA，file->open
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680608438600-9eb02540-01da-4216-bed7-0111a756507c.png)
+ 选中工程目录，Open
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680608485389-33e370db-26c0-4f9f-a5c8-0a41f1a3e9ac.png)
+ 等待 IDEA 编译完成，点击右侧标签的 maven 标签，点开 hobbits-bz-orm，点开Lifecycle，双击 install
+ 此处目的是为了将 orm 的 jar 在本地进行编译打包，让 hobbits-bz-mgr 工程能够识别
+ install执行之后 maven 会将工程打包到本地仓库，本地仓库一般是在 .m2 目录中
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680608654001-7fc70fe6-289d-4339-87f7-aaf56aed27a1.png)
+ 如果需要通过接口进行直接调试的情况下，为了方便起见可以先屏蔽用户 登录 的各种check，注释掉如下类即可：
    - ![](https://cdn.nlark.com/yuque/0/2022/png/21843987/1660017749869-b379c377-ad85-46d5-af6e-1be80d0c18f5.png)
    - ![](https://cdn.nlark.com/yuque/0/2022/png/21843987/1660017777545-51cc6df8-f635-49c0-a707-508507a14154.png)
    - ![](https://cdn.nlark.com/yuque/0/2022/png/21843987/1660017785071-e793bcd9-6e04-47b1-8fe6-cf447bb2ca0f.png)
    - ![](https://cdn.nlark.com/yuque/0/2022/png/21843987/1660017798133-d29e1683-84f7-43f4-9d1d-15c582ba7935.png)
+ 修改 mgr 工程中的 yml 文件
    - 目录：
        * ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680760499160-94189562-f603-4f3a-84b1-caf72fd4bd0a.png)
    - 修改内容 application.service.ms 下的外调服务的 URL
        * 其中 url 的 端口号 又对应每个工程 yml 文件中的设置，如：ms-base
        * ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680765534041-251f732d-ef6e-4c63-b958-3367928972d4.png)
        * 这个 port 设置成什么，当前工程启动后的访问 port 就是什么

```yaml
      #基础微服务-基础中心
      base: ${application.service.ms.basic}/base
      #基础微服务
      basic: http://localhost:8080/
      #基础微服务-配置中心
      configuration: ${application.service.ms.basic}/configuration
      #财务中心微服务
      finance: http://localhost:8087/
      #网关微服务
      gateway: http://hobbits.ishopex.cn/gateway
      #商品中心微服务
      goods: http://localhost:8083/
      #接口中心微服务
      harbor: http://localhost:8086/
      #库存中心微服务
      inventory: http://localhost:8082/
      #标签中心微服务
      label: http://localhost:8085/
      #会员中心微服务
      member: http://localhost:8180/
      #消息中心微服务
      message: http://localhost:8084/
      #订单中心微服务
      order: http://localhost:8081/
      #基础微服务-权限中心
      permission: ${application.service.ms.basic}/per
      #采购中心微服务
      purchase: http://localhost:8088/
      #报表中心微服务
      report: http://localhost:8089/
```

+ mgr 启动，运行 Application 的 main 方法（**<font style="color:#DF2A3F;">先要启动 ms-base</font>**）
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680766077547-94ed1c2d-a8fb-48eb-8862-c8cbb1b4176b.png)
+ 接下来导入 ms-base 工程，基本步骤是相同的，只是 ms-base 不需要再注释掉以上的类
+ 导入相关目录下的工程
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680760102053-202c4863-9346-49a5-a737-f9c05c30348b.png)
+ 等待 IDEA 编译完成，点击右侧标签的 maven 标签，点开 hobbits-bz-orm，点开Lifecycle，双击 install
+ ms-base 启动，运行 Application 的 main 方法（**<font style="color:#DF2A3F;">注意 port 端口号，同一台机器启动多个服务，端口号不能重复</font>**）

#### 其他依赖包工程
    - hobbits-lib-toolkit
        * 目前封装了 MQ 的配置与共通方法
    - hobbits-ms-library
        * 一些共通的工具类
    - 这些工程也需要再IDEA中打开，并且需要通过 MAVEN 进行 install 操作，install 后会打包到本地 .m2 文件夹中
    - 特别说明：
        * .m2 文件夹是 maven 自动生成的本地 jar 包仓库
        * 所有 pom 文件中 dependence 的 jar 包，都会下载到 .m2 文件夹中

#### 通用的一些修改 application.yml
+ 数据库
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680771027600-c37bc195-adc3-4086-92e9-bd911d1b20ee.png)
    - 修改 url，username，password
+ 设置 RabbitMQ 和 redis
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680766713949-bd45c8a0-97ee-42f1-a630-f81014f8daa9.png)
+ 设置 RocketMQ（Rabbit和Rocket二选一）
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680772276254-b92c3962-bdfa-4c3b-ab29-9310bc5ac0da.png)
+ redission 本地启动工程时需要注意
    - 频繁启动会导致开发环境的 redis 线程来不及回收引起的启动链接 redis 超时，修改此参数保证本地开发环境启动不报错
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680771062782-09626193-223f-4b8c-bca4-7bd1938e0431.png)

#### yml 关于 MQ 与 java properties 类映射的说明
+ yml
    - ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680767184133-39904946-d0a5-494b-b8a3-9fa448875cb1.png)
+ 映射的java properties
+ ![](https://cdn.nlark.com/yuque/0/2023/png/21843987/1680767221496-1050d024-84f7-42f8-957d-17e89478067e.png)
+ 举例：
    - 需要先设置 @ConfigurationProperties 确定需要映射的 yml 文件的前缀
    - yml文件中的 goods-sync-data 会自动映射到 goodSyncData
    - goods-sync-data 下的内容会自动映射到 goodSyncData 的成员变量

