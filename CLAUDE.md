# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is the DJOS (Distributed Java Operation System) project - a comprehensive microservices ecosystem built with Spring Boot and Spring Cloud. The system follows a multi-module Maven architecture with clear separation of concerns across different business domains.

## Technology Stack

- **Framework**: Spring Boot 2.5.14, Spring Cloud 2020.0.3
- **Database**: MySQL 8.0 with MyBatis 2.2.0 ORM
- **Connection Pool**: Druid 1.2.6
- **Message Queue**: RocketMQ 4.9.4
- **Cache**: Redis with Redisson 3.16.4
- **Service Discovery**: Nacos 2.0.4
- **Task Scheduling**: XXL-Job 2.3.3
- **Documentation**: Swagger 3.0.0
- **Monitoring**: Prometheus with Micrometer
- **Build Tool**: Maven 3.x
- **Java Version**: Java 8

## Common Development Commands

### Build Commands
```bash
# Build entire project
mvn clean install

# Build specific module
cd hobbits-ms-harbor
mvn clean package

# Skip tests during build
mvn clean package -DskipTests

# Build Docker image
docker build -t hobbits-ms-harbor ./hobbits-ms-harbor
```

### Testing Commands
```bash
# Run all tests
mvn test

# Run tests for specific module
cd hobbits-ms-harbor && mvn test

# Run specific test class
mvn test -Dtest=MaterialServiceTest

# Integration tests
mvn verify
```

### Database Commands
```bash
# Start development environment
docker-compose up -d

# Liquibase migrations (for manager module)
cd manager
mvn liquibase:update

# Generate database changelog
mvn liquibase:generateChangeLog
```

### Development Environment
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f [service-name]

# Stop all services
docker-compose down
```

## Architecture Overview

### Microservices Structure
The system follows a domain-driven design with separate microservices for each business domain:

- **hobbits-ms-gateway**: API Gateway for external requests
- **hobbits-bz-gateway**: Business gateway for internal routing
- **hobbits-ms-harbor**: Interface center service (avoids 'interface' keyword)
- **hobbits-ms-goods**: Product management service
- **hobbits-ms-order**: Order processing service
- **hobbits-ms-inventory**: Inventory management service
- **hobbits-ms-member**: Member management service
- **hobbits-ms-finance**: Financial service
- **hobbits-ms-message**: Message service
- **hobbits-ms-label**: Label management service
- **hobbits-ms-adapter**: External system adapter
- **hobbits-canal-client**: Database change data capture
- **hobbits-xxl-job**: Distributed task scheduling
- **manager**: Database management and migrations

### Single Service Architecture
Each microservice follows a consistent three-layer architecture:

```
service-name/
├── orm/                   # Data Access Layer
│   ├── model/            # Entity classes (@Entity)
│   └── mapper/           # MyBatis mappers (@Mapper)
├── library/              # Common utilities
│   ├── dto/              # Data Transfer Objects
│   └── enums/            # Enumerations
└── platform/             # Application Layer
    ├── controller/       # REST controllers (@RestController)
    ├── biz/              # Business logic (@Service, @Component)
    └── config/           # Configuration classes
```

### Key Patterns

#### 1. Layered Architecture
- **Controller Layer**: Handles HTTP requests, validation, response formatting
- **Biz Layer**: Business logic, transaction management, service orchestration
- **Mapper Layer**: Data access operations using MyBatis annotations

#### 2. Dependency Injection
- Use `@Autowired` or constructor injection for dependencies
- Services are annotated with `@Service` or `@Component`
- Mappers use `@Mapper` annotation

#### 3. Transaction Management
```java
@Transactional(rollbackFor = Exception.class)
public void createOrder(OrderCreateRequest request) {
    // Business logic with automatic rollback
}
```

#### 4. Exception Handling
- Custom exceptions for different error scenarios
- Global exception handler with `@ControllerAdvice`
- Standardized API response format

#### 5. API Design
- RESTful endpoints with standard HTTP methods
- Request/Response DTOs for data transfer
- Swagger documentation for API contracts

## Configuration Management

### Environment Profiles
- `application.yml` - Base configuration
- `application-dev.yml` - Development environment
- `application-prod.yml` - Production environment
- `application-uat.yml` - UAT environment

### Key Configuration Files
- `bootstrap.yml` - Service discovery and configuration
- `logback-spring.xml` - Logging configuration
- `Dockerfile` - Container build configuration

## Database Schema Management

### Master Data Management
The `digios-master-data` directory contains versioned database schemas:
- `2.23.0/` - Schema version 2.23.0 with YAML adapter definitions
- `2.24.0/` - Schema version 2.24.0 with updated adapters

### Liquibase Integration
The `manager` module uses Liquibase for database version control:
- Changelogs stored in `src/main/resources/db/changelog/`
- Migration scripts follow naming convention: `YYYYMMDDHHMMSS_description.xml`

## Development Guidelines

### Code Organization
- Follow the established package structure: `cn.shopex.hobbits.[domain]`
- Use meaningful class and method names
- Keep business logic in the Biz layer, not in controllers

### API Standards
- Use `@Valid` for request body validation
- Return `ResponseEntity<ApiResponse<T>>` for consistent responses
- Include proper HTTP status codes

### Database Practices
- Use MyBatis annotations for simple queries
- Use XML mappers for complex queries
- Implement proper connection pooling with Druid

### Testing Strategy
- Unit tests for business logic
- Integration tests for API endpoints
- Test containers for database-dependent tests

## Common Issues and Solutions

### Port Conflicts
- MySQL: 3306
- Redis: 6379
- RocketMQ NameServer: 9876
- Nacos: 8848
- Ensure ports are available before starting services

### Service Discovery
- All services register with Nacos
- Use service names for inter-service communication
- Configure proper load balancing

### Message Queue
- RocketMQ used for asynchronous processing
- Implement proper message consumers with ack/retry logic
- Monitor message backlog and processing times

## Learning Resources

The project includes comprehensive learning materials in `docs/learning/`:
1. Environment setup
2. Project structure understanding
3. Data access layer patterns
4. Business logic implementation
5. Controller layer design
6. Service communication
7. Configuration management
8. Database version control
9. Task scheduling
10. Comprehensive practice

## Deployment

### Docker Deployment
- Each service has its own Dockerfile
- Use `docker-compose.yml` for local development
- Kubernetes manifests in `hobbits-deploy/` for production

### CI/CD Pipeline
- Jenkins files in each service directory
- Automated builds and tests
- Container registry integration

## Monitoring and Observability

### Metrics Collection
- Prometheus metrics with Micrometer
- JVM metrics and custom business metrics
- Grafana dashboards in `hobbits-deploy/grafana/`

### Logging
- Structured logging with Logback
- Log aggregation and centralized management
- Correlation IDs for request tracing