version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: djos
      MYSQL_USER: djos
      MYSQL_PASSWORD: djos123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - djos-network
    command: --default-authentication-plugin=mysql_native_password --skip-host-cache --skip-name-resolve

  redis:
    image: redis:6.2
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - djos-network

  nacos:
    image: nacos/nacos-server:2.0.4
    container_name: nacos
    ports:
      - "8848:8848"
    environment:
      MODE: standalone
      SPRING_DATASOURCE_PLATFORM: mysql
      MYSQL_SERVICE_HOST: mysql
      MYSQL_SERVICE_PORT: 3306
      MYSQL_SERVICE_DB_NAME: nacos
      MYSQL_SERVICE_USER: root
      MYSQL_SERVICE_PASSWORD: root123
      NACOS_AUTH_ENABLE: true
      NACOS_AUTH_TOKEN: SecretKey012345678901234567890123456789012345678901234567890123456789
      NACOS_AUTH_IDENTITY_KEY: nacos
      NACOS_AUTH_IDENTITY_VALUE: nacos
    volumes:
      - nacos_logs:/home/<USER>/logs
    networks:
      - djos-network
    depends_on:
      - mysql

  rocketmq-nameserver:
    image: foxiswho/rocketmq:server-4.9.4
    container_name: rocketmq-nameserver
    ports:
      - "9876:9876"
    volumes:
      - rocketmq_nameserver_logs:/home/<USER>/logs
    networks:
      - djos-network

  rocketmq-broker:
    image: foxiswho/rocketmq:broker-4.9.4
    container_name: rocketmq-broker
    ports:
      - "10909:10909"
      - "10911:10911"
    environment:
      NAMESRV_ADDR: rocketmq-nameserver:9876
    volumes:
      - rocketmq_broker_logs:/home/<USER>/logs
      - rocketmq_broker_store:/home/<USER>/store
    networks:
      - djos-network
    depends_on:
      - rocketmq-nameserver

  rocketmq-console:
    image: styletang/rocketmq-console-ng
    container_name: rocketmq-console
    ports:
      - "8081:8080"
    environment:
      JAVA_OPTS: -Drocketmq.namesrv.addr=rocketmq-nameserver:9876 -Dcom.rocketmq.sendMessageWithVIPChannel=false
    networks:
      - djos-network
    depends_on:
      - rocketmq-nameserver

networks:
  djos-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  nacos_logs:
    driver: local
  rocketmq_nameserver_logs:
    driver: local
  rocketmq_broker_logs:
    driver: local
  rocketmq_broker_store:
    driver: local