version: '3.8'

services:
  mysql:
    image: mysql:8.0
    container_name: mysql
    ports:
      - "3306:3306"
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: djos
      MYSQL_USER: djos
      MYSQL_PASSWORD: djos123
    volumes:
      - mysql_data:/var/lib/mysql
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - djos-network
    command: --default-authentication-plugin=mysql_native_password --skip-host-cache --skip-name-resolve

  redis:
    image: redis:6.2
    container_name: redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - djos-network

networks:
  djos-network:
    driver: bridge

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
